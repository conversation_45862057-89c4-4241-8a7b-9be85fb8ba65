import { chromium, firefox, webkit, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext } from 'playwright';
import { RpaFlow, RpaStep, FlowSettings, ExecutionLog } from '@rpa-project/shared';
import { BaseRunner, RunnerContext, StepExecutionResult } from '../base';
import { STEP_RUNNER_MAPPING } from '../registry/stepTypes';
import {
  executeNavigate,
  executeGoBack,
  executeGoForward,
  executeReload,
  executeClick,
  executeFill,
  executeType,
  executeSelectOption,
  executeCheck,
  executeUncheck,
  executeFillPassword,
  executeFill2FA,
  executeExtractText,
  executeExtractAttribute,
  executeTakeScreenshot,
  executeDownloadFile,
  executeWaitForSelector,
  executeWaitForTimeout,
  executeWaitForUrl,
  executeIfElementExists,
  executeConditionalClick
} from './stepExecutors';

export interface PlaywrightRunnerContext {
  browser: Browser;
  context: BrowserContext;
  page: Page;
  variables: Record<string, any>;
  onLog: (log: Omit<ExecutionLog, 'timestamp'>) => void;
}

/**
 * Enhanced PlaywrightRunner with modular step executors
 */
export class PlaywrightRunner extends BaseRunner {
  private playwrightContext?: PlaywrightRunnerContext;
  private lastStepWasPlaywright = false;

  async initialize(settings: FlowSettings, variables: Record<string, any> = {}): Promise<void> {
    // Reset delay tracking for new flow execution
    this.lastStepWasPlaywright = false;

    const browserType = settings.browser || 'chromium';
    const headless = settings.headless !== false;

    let browser: Browser;
    switch (browserType) {
      case 'firefox':
        browser = await firefox.launch({ headless });
        break;
      case 'webkit':
        browser = await webkit.launch({ headless });
        break;
      default:
        browser = await chromium.launch({ headless });
    }

    const contextOptions: any = {};
    
    if (settings.viewport) {
      contextOptions.viewport = settings.viewport;
    }
    
    if (settings.userAgent) {
      contextOptions.userAgent = settings.userAgent;
    }
    
    if (settings.locale) {
      contextOptions.locale = settings.locale;
    }
    
    if (settings.timezone) {
      contextOptions.timezoneId = settings.timezone;
    }

    const context = await browser.newContext(contextOptions);
    const page = await context.newPage();

    this.playwrightContext = {
      browser,
      context,
      page,
      variables: { ...variables },
      onLog: this.logHandler
    };
  }

  getSupportedStepTypes(): string[] {
    return Object.keys(STEP_RUNNER_MAPPING).filter(
      stepType => STEP_RUNNER_MAPPING[stepType as keyof typeof STEP_RUNNER_MAPPING] === 'playwright'
    );
  }

  /**
   * Override executeFlow to add web automation specific delays between steps
   */
  async executeFlow(flow: RpaFlow, variables: Record<string, any> = {}): Promise<Record<string, any>> {
    await this.initialize(flow.settings || {}, variables);

    const context: RunnerContext = {
      variables: { ...variables },
      onLog: this.logHandler,
      cancellationChecker: this.cancellationChecker || undefined,
      flowId: flow.id
    };

    try {
      for (let i = 0; i < flow.steps.length; i++) {
        // Check for cancellation before each step
        if (context.cancellationChecker) {
          const isCancelled = await context.cancellationChecker();
          if (isCancelled) {
            context.onLog({
              level: 'info',
              message: 'Execution was cancelled, stopping flow'
            });
            throw new Error('Execution was cancelled');
          }
        }

        const step = flow.steps[i];

        // Only execute steps this runner can handle
        if (!this.canHandleStep(step.type)) {
          context.onLog({
            level: 'warn',
            message: `Step type '${step.type}' not supported by PlaywrightRunner`,
            stepId: step.id
          });
          continue;
        }

        const result = await this.executeStep(step, context);

        if (!result.success) {
          throw new Error(result.error || `Step execution failed: ${step.type}`);
        }

        // Update context variables with any results from the step
        if (result.variables) {
          context.variables = { ...context.variables, ...result.variables };
        }

        // Add random delay between web automation steps (except after the last step)
        if (i < flow.steps.length - 1) {
          const delay = this.getRandomDelay();
          context.onLog({
            level: 'info',
            message: `Waiting ${delay}ms before next step...`,
            stepId: step.id
          });
          await this.sleep(delay);
        }
      }

      return context.variables;
    } catch (error) {
      // Re-throw the error, cleanup will be handled by the caller
      throw error;
    }
  }

  async executeStep(step: RpaStep, context: RunnerContext): Promise<StepExecutionResult> {
    if (!this.playwrightContext) {
      throw new Error('PlaywrightRunner not initialized');
    }

    // Add random delay before each RPA step (except the first one in a session)
    if (this.lastStepWasPlaywright) {
      const delay = this.getRandomDelay();
      context.onLog({
        level: 'info',
        message: `Waiting ${delay}ms before next RPA step...`,
        stepId: step.id
      });
      await this.sleep(delay);
    }

    // Mark that this step is a Playwright step for next time
    this.lastStepWasPlaywright = true;

    const { page, variables, onLog } = this.playwrightContext;

    onLog({
      level: 'info',
      message: `Executing step: ${step.type}`,
      stepId: step.id
    });

    try {
      // Create executor context
      const executorContext = {
        page,
        variables: context.variables,
        onLog,
        interpolateVariables: this.interpolateVariables.bind(this),
        executeStep: this.executeStep.bind(this),
        flowId: context.flowId
      };

      // Route to appropriate step executor
      switch (step.type) {
        // Navigation steps
        case 'navigate':
          return await executeNavigate(step as any, executorContext);
        case 'goBack':
          return await executeGoBack(step, executorContext);
        case 'goForward':
          return await executeGoForward(step, executorContext);
        case 'reload':
          return await executeReload(step, executorContext);

        // Interaction steps
        case 'click':
          return await executeClick(step as any, executorContext);
        case 'fill':
          return await executeFill(step as any, executorContext);
        case 'type':
          return await executeType(step as any, executorContext);
        case 'selectOption':
          return await executeSelectOption(step as any, executorContext);
        case 'check':
          return await executeCheck(step as any, executorContext);
        case 'uncheck':
          return await executeUncheck(step as any, executorContext);
        case 'fillPassword':
          return await executeFillPassword(step as any, executorContext);
        case 'fill2FA':
          return await executeFill2FA(step as any, executorContext);

        // Extraction steps
        case 'extractText':
          return await executeExtractText(step as any, executorContext);
        case 'extractAttribute':
          return await executeExtractAttribute(step as any, executorContext);
        case 'takeScreenshot':
          return await executeTakeScreenshot(step as any, executorContext);
        case 'downloadFile':
          return await executeDownloadFile(step as any, executorContext);

        // Waiting steps
        case 'waitForSelector':
          return await executeWaitForSelector(step as any, executorContext);
        case 'waitForTimeout':
          return await executeWaitForTimeout(step as any, executorContext);
        case 'waitForUrl':
          return await executeWaitForUrl(step as any, executorContext);

        // Conditional steps
        case 'ifElementExists':
          return await executeIfElementExists(step as any, executorContext);
        case 'conditionalClick':
          return await executeConditionalClick(step as any, executorContext);

        default:
          return {
            success: false,
            error: `Unsupported step type: ${step.type}`
          };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      onLog({
        level: 'error',
        message: `Error executing step ${step.type}: ${errorMessage}`,
        stepId: step.id
      });

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  async cleanup(): Promise<void> {
    if (this.playwrightContext) {
      try {
        await this.playwrightContext.context.close();
        await this.playwrightContext.browser.close();
      } catch (error) {
        console.error('Error during PlaywrightRunner cleanup:', error);
      }
      this.playwrightContext = undefined;
    }
  }

  /**
   * Generate random delay between 1000-10000ms for web automation steps
   */
  private getRandomDelay(): number {
    return Math.floor(Math.random() * (5000 - 1000 + 1)) + 1000;
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
